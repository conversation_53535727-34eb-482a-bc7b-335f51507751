# Generic Extraction Example

A complete example demonstrating structured data extraction with integrated evaluation using the SOME library.

## 🎯 What This Example Shows

- **Product data extraction** from text descriptions
- **Premade evaluation integration** with just 2-3 lines of code
- **Complete metrics collection** (LLM performance + data quality)
- **Custom language model registration** (optional)

## 🚀 Quick Start

```bash
python -m some.examples.generic_extraction.run_extraction
```

## 🔧 Key Integration: Premade Evaluation

This example demonstrates how to integrate the premade evaluation system into any extraction pipeline with minimal code:

```python
# Import the premade evaluation components
from some.premade.extraction_evaluation import BasicEvaluation, EvaluationPrompt

# Integration is just 2-3 lines:
formatted_records = [EvaluationPrompt().build(format_evaluation_record(r)) for r in zip(inputs, results)]
evaluation_results, _, evaluation_time = lm.generate(formatted_records)
```

### Before vs After

**Before (Custom Evaluation):**
- Custom `EvaluationPrompt` class in `my_prompt.py`
- Custom `BasicEvaluation` schema in `my_schema.py`
- ~25 lines of custom evaluation code

**After (Premade Evaluation):**
- Import from `some.premade.extraction_evaluation`
- 2-3 lines of integration code
- Reusable across all extraction pipelines

## 📁 File Structure

```
generic_extraction/
├── my_schema.py           # ProductSpec schema only
├── my_prompt.py           # ProductPrompt only (evaluation removed)
├── my_language_model.py   # Custom model registration
├── run_extraction.py      # Main script with premade evaluation
└── README.md             # This file
```

## 🔍 Evaluation Results

The premade evaluation provides simple, clear results:

```
Evaluation Results:
0: Correct=True, Formatted=True
1: Correct=True, Formatted=True  
2: Correct=True, Formatted=True
```

With detailed metrics:
- **Correct extractions**: Factual accuracy assessment
- **Properly formatted**: Schema compliance check
- **Confidence scores**: Optional confidence ratings
- **Reasoning**: Optional explanations

## 🛠 Customization

### Using Different Models

```python
# OpenAI (default)
lm = get_language_model(provider="openai", model="gpt-4o-mini")

# Custom model (registered in my_language_model.py)
lm = get_language_model(provider="custom", model="mock-model")

# Ollama local
lm = get_language_model(provider="ollama", model="llama3:8b")
```

### Adapting the Evaluation Format

The `format_evaluation_record` function converts your extraction results to the format expected by the premade evaluation:

```python
def format_evaluation_record(record):
    """Format a record for evaluation using the premade evaluation format."""
    input_prompt = record[0]["messages"][0]["content"]
    expected_format = record[0]["response_format"].model_json_schema()
    extraction_output = record[1]["product"]

    return {
        "original_text": input_prompt,
        "extraction_prompt": "Extract ProductSpec as JSON...",
        "expected_schema": expected_format,
        "extraction_output": extraction_output
    }
```

### Adding Your Own Data

Replace the sample data in `main()`:

```python
items = [
    {"text": "Your product description 1..."},
    {"text": "Your product description 2..."},
    # Add more items
]
```

## 📊 Metrics Output

The example provides comprehensive metrics:

### LLM Performance Metrics
- Token usage and costs
- Processing times
- Success rates
- Worker utilization

### Data Quality Metrics  
- Field coverage analysis
- Value distributions
- Completeness assessment
- Evaluation accuracy rates

## 💡 Key Benefits of Premade Evaluation

1. **Minimal Integration**: Just 2-3 lines of code
2. **Consistent Results**: Same evaluation logic across all pipelines
3. **Reusable**: Works with any extraction schema
4. **Simple Output**: Clear boolean results with optional details
5. **Maintainable**: Centralized evaluation logic

## 🔗 Related Examples

- [Premade Templates](../../premade/) - Ready-to-run examples including evaluation
- [Simple Product Extraction](../../premade/simple_product_extraction/) - Basic extraction template
- [Extraction Evaluation](../../premade/extraction_evaluation/) - Standalone evaluation example

---

**Ready to integrate evaluation into your pipeline?** Just import and use! 🎉
