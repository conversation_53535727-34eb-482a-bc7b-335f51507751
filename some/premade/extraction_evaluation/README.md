# Extraction Evaluation

A comprehensive system for evaluating extraction results against schemas and quality criteria using the SOME library.

## 🎯 What This Example Does

Evaluates extraction quality on two key criteria:
- **Correct**: Is the extracted information factually accurate?
- **Formatted**: Does the output follow the expected schema properly?

## 🚀 Quick Start

```bash
# Run the evaluation example
python -m some.premade.extraction_evaluation.run_evaluation

# Or import and use in your code
from some.premade.extraction_evaluation import main
results = main()
```

## 📋 Sample Evaluation Scenario

**Original Text:**
```
"Tesla Model 3 Long Range offers up to 358 miles of EPA-estimated range. 
It accelerates from 0-60 mph in 4.2 seconds and has a top speed of 145 mph. 
Starting price is $47,740."
```

**Expected Schema:**
```json
{
  "model": "string",
  "range_miles": "number", 
  "acceleration_0_60": "number",
  "price": "number"
}
```

**Extraction Output:**
```json
{
  "model": "Tesla Model 3 Long Range",
  "range_miles": 400,  // ❌ Incorrect: should be 358
  "acceleration_0_60": 3.8,  // ❌ Incorrect: should be 4.2  
  "price": 47740  // ✅ Correct
}
```

**Evaluation Result:**
```json
{
  "correct": false,
  "formatted": true,
  "reasoning": "Extraction contains factual inaccuracies: range should be 358 miles, not 400; acceleration should be 4.2 seconds, not 3.8",
  "confidence": 0.92
}
```

## 🏗 Architecture

### Files Structure
```
extraction_evaluation/
├── __init__.py          # Package exports
├── schema.py            # Evaluation schemas and criteria
├── prompt.py            # EvaluationPrompt builder
├── run_evaluation.py    # Main execution with test scenarios
└── README.md           # This file
```

### Key Components

#### 1. Schema (`schema.py`)
- **BasicEvaluation**: Simple evaluation result schema with four fields
  - `correct`: Boolean indicating factual accuracy
  - `formatted`: Boolean indicating schema compliance
  - `reasoning`: Optional explanation of the evaluation
  - `confidence`: Optional confidence score (0.0-1.0)

#### 2. Prompt Builder (`prompt.py`)
- **EvaluationPrompt**: Builds simple evaluation prompts
- Focuses on correctness and formatting compliance
- Clear, concise evaluation instructions

#### 3. Execution Script (`run_evaluation.py`)
- Four test scenarios with different quality levels
- Complete evaluation pipeline
- Simple, clear results analysis

## 🛠 Customization

### Adding Custom Evaluation Criteria

1. **Extend the criteria enum:**
```python
class EvaluationCriteria(str, Enum):
    ACCURACY = "accuracy"
    COMPLETENESS = "completeness" 
    FORMAT_COMPLIANCE = "format_compliance"
    CONSISTENCY = "consistency"
    RELEVANCE = "relevance"
    # Add your custom criteria
    DOMAIN_SPECIFICITY = "domain_specificity"
    BUSINESS_LOGIC = "business_logic"
```

2. **Update evaluation prompt:**
```python
prompt_text = f"""Evaluate extraction on these criteria:
1. ACCURACY: Factual correctness
2. COMPLETENESS: All information extracted
3. FORMAT_COMPLIANCE: Schema adherence
4. DOMAIN_SPECIFICITY: Industry-specific accuracy
5. BUSINESS_LOGIC: Business rule compliance
...
"""
```

### Creating Domain-Specific Evaluations

```python
def build_medical_evaluation_prompt(item):
    """Evaluation prompt for medical data extraction."""
    return {
        "prompt_text": f"""Evaluate medical data extraction with focus on:
        - Clinical accuracy and terminology
        - Patient safety implications  
        - Regulatory compliance
        - Data sensitivity handling
        
        Original text: {item['original_text']}
        Extraction: {item['extraction_output']}
        """,
        "response_format": ExtractionEvaluation,
        "result_key": "medical_evaluation"
    }
```

### Adding Your Own Test Scenarios

```python
def get_custom_scenarios():
    return [
        {
            "scenario_name": "Your Test Case",
            "original_text": "Source text to evaluate...",
            "extraction_prompt": "Prompt used for extraction...",
            "expected_schema": {"field": "type"},
            "extraction_output": {"field": "extracted_value"},
            "evaluation_context": "Additional context for evaluation"
        }
    ]
```

## 📊 Understanding Evaluation Results

### Basic Assessment
- **Correct**: Boolean indicating if extracted information is factually accurate
- **Formatted**: Boolean indicating if output follows the expected schema
- **Reasoning**: Optional explanation of the evaluation decision
- **Confidence**: Optional confidence score from 0.0 to 1.0

### Quality Analysis
- **Correctness Rate**: Percentage of extractions that are factually accurate
- **Format Compliance**: Percentage of extractions that follow schema properly
- **Average Confidence**: Mean confidence across evaluations with confidence scores

## 🎛 Configuration Options

### Evaluation Sensitivity

```python
# Strict evaluation
prompt_text = """Be very strict in evaluation. Mark as unacceptable 
if any factual errors or format violations exist."""

# Lenient evaluation  
prompt_text = """Focus on major issues. Minor formatting problems 
are acceptable if core information is correct."""
```

### Custom Scoring Weights

```python
# Modify the evaluation prompt to emphasize certain criteria
prompt_text = f"""Weight the criteria as follows:
- Accuracy: 40% (most important)
- Completeness: 30% 
- Format Compliance: 20%
- Consistency: 10%
"""
```

## 🔍 Use Cases

### 1. Quality Assurance
Evaluate extraction pipelines before production deployment:
```python
# Test your extraction system
extraction_results = your_extraction_pipeline(test_data)
evaluation_results = evaluate_extractions(extraction_results)
quality_score = calculate_average_score(evaluation_results)
```

### 2. Model Comparison
Compare different language models or prompts:
```python
models = ["gpt-4o-mini", "gpt-4o", "claude-3-haiku"]
for model in models:
    results = run_extraction_with_model(model, test_data)
    evaluations = evaluate_results(results)
    print(f"{model}: {average_score(evaluations):.2f}")
```

### 3. Continuous Monitoring
Monitor extraction quality over time:
```python
# Daily quality check
daily_extractions = get_recent_extractions()
daily_evaluations = evaluate_batch(daily_extractions)
if average_score(daily_evaluations) < threshold:
    alert_quality_team()
```

## 🚨 Common Evaluation Patterns

### High Accuracy, Low Completeness
- Extraction is correct but misses information
- **Fix**: Improve prompt to request comprehensive extraction

### High Completeness, Low Accuracy  
- Extraction captures everything but with errors
- **Fix**: Add validation steps or use more capable model

### Format Violations
- Correct information in wrong structure
- **Fix**: Improve schema documentation in prompts

### Inconsistent Results
- Similar inputs produce different output formats
- **Fix**: Add examples and stricter formatting instructions

## 🔧 Advanced Features

### Batch Evaluation
```python
def evaluate_batch(extraction_results, batch_size=10):
    """Evaluate multiple extractions efficiently."""
    evaluations = []
    for batch in chunk_list(extraction_results, batch_size):
        batch_evaluations = run_evaluation_batch(batch)
        evaluations.extend(batch_evaluations)
    return evaluations
```

### Custom Issue Detection
```python
def detect_custom_issues(extraction, expected_schema):
    """Add domain-specific issue detection."""
    issues = []
    
    # Example: Check for required business fields
    if extraction.get('price') and extraction['price'] <= 0:
        issues.append({
            "criterion": "business_logic",
            "severity": "critical",
            "description": "Price must be positive"
        })
    
    return issues
```

## 🚀 Next Steps

1. **Run the example**: See evaluation in action with test scenarios
2. **Add your scenarios**: Create test cases from your domain
3. **Customize criteria**: Define evaluation dimensions for your use case
4. **Integrate with pipelines**: Add evaluation to your extraction workflows
5. **Monitor quality**: Set up continuous evaluation for production systems

## 🔗 Related Examples

- [Simple Product Extraction](../simple_product_extraction/) - Generate data to evaluate
- [Generic Extraction](../../generic_extraction/) - More extraction examples
- [Schema Metrics Guide](../../../docs/SCHEMA_METRICS.md) - Advanced analysis

---

**Ready to evaluate extraction quality?** Run the example and start improving! 🔍
