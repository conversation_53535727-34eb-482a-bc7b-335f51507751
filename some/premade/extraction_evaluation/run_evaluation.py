"""
Extraction Evaluation - Ready-to-Run Example

This script demonstrates how to evaluate extraction results using the SOME library.
It includes sample extraction scenarios with both good and problematic outputs
to showcase the evaluation capabilities.

Run this example:
    python -m some.examples.premade.extraction_evaluation.run_evaluation
"""
from __future__ import annotations

import j<PERSON>
from typing import Dict, Any, List

from some.inference import get_language_model
from some.metrics import LLMMetricsCollector, SchemaMetricsCollector
from .schema import BasicEvaluation
from .prompt import EvaluationPrompt

def get_sample_evaluation_scenarios() -> List[Dict[str, Any]]:
    """
    Get sample extraction scenarios for evaluation.
    
    Returns various extraction results with different quality levels
    to demonstrate the evaluation system.
    """
    return [
        {
            "scenario_name": "Good Product Extraction",
            "original_text": "The Apple MacBook Pro 16-inch features the M3 Max chip, 36GB unified memory, "
                           "and 1TB SSD storage. Starting at $3,999, it includes a Liquid Retina XDR display "
                           "with 3456x2234 resolution and up to 22 hours of battery life. Available in Space Black and Silver.",
            "extraction_prompt": "Extract product information including name, price, features, and specifications.",
            "expected_schema": {
                "name": "string",
                "price": "number", 
                "features": "array of strings",
                "specifications": "object"
            },
            "extraction_output": {
                "name": "Apple MacBook Pro 16-inch",
                "price": 3999,
                "features": ["M3 Max chip", "36GB unified memory", "1TB SSD storage", "Liquid Retina XDR display", "22 hours battery life"],
                "specifications": {
                    "display_resolution": "3456x2234",
                    "colors": ["Space Black", "Silver"]
                }
            }
        },
        {
            "scenario_name": "Incomplete Extraction",
            "original_text": "Nike Air Jordan 1 Retro High OG in Chicago colorway. Features premium leather upper, "
                           "Air-Sole unit in heel, and rubber outsole. Originally released in 1985, "
                           "this iconic basketball shoe retails for $170. Size range: 7-18. Limited stock available.",
            "extraction_prompt": "Extract complete product information including all mentioned details.",
            "expected_schema": {
                "name": "string",
                "price": "number",
                "brand": "string", 
                "features": "array of strings",
                "availability": "string",
                "size_range": "string"
            },
            "extraction_output": {
                "name": "Air Jordan 1",
                "price": 170,
                "features": ["leather upper", "Air-Sole unit"]
                # Missing: brand, availability, size_range, colorway info
            }
        },
        {
            "scenario_name": "Format Violation",
            "original_text": "Samsung Galaxy S24 Ultra smartphone with 200MP camera, 6.8-inch Dynamic AMOLED display, "
                           "and S Pen support. Available in Titanium Gray, Titanium Black, Titanium Violet, "
                           "and Titanium Yellow. Price starts at $1,299.99 for 256GB model.",
            "extraction_prompt": "Extract smartphone specifications in structured format.",
            "expected_schema": {
                "name": "string",
                "price": "number",
                "display_size": "number", 
                "camera_mp": "number",
                "colors": "array of strings"
            },
            "extraction_output": {
                "name": "Samsung Galaxy S24 Ultra",
                "price": "$1,299.99",  # Should be number, not string
                "display_size": "6.8-inch",  # Should be number, not string with unit
                "camera_mp": "200MP camera",  # Should be number, not string
                "colors": "Titanium Gray, Titanium Black, Titanium Violet, Titanium Yellow"  # Should be array
            }
        },
        {
            "scenario_name": "Inaccurate Information",
            "original_text": "Tesla Model 3 Long Range offers up to 358 miles of EPA-estimated range. "
                           "It accelerates from 0-60 mph in 4.2 seconds and has a top speed of 145 mph. "
                           "Starting price is $47,740. Features include Autopilot, 15-inch touchscreen, "
                           "and over-the-air software updates.",
            "extraction_prompt": "Extract vehicle specifications and features accurately.",
            "expected_schema": {
                "model": "string",
                "range_miles": "number",
                "acceleration_0_60": "number",
                "top_speed": "number", 
                "price": "number",
                "features": "array of strings"
            },
            "extraction_output": {
                "model": "Tesla Model 3 Long Range",
                "range_miles": 400,  # Incorrect: should be 358
                "acceleration_0_60": 3.8,  # Incorrect: should be 4.2
                "top_speed": 155,  # Incorrect: should be 145
                "price": 47740,  # Correct
                "features": ["Autopilot", "15-inch touchscreen", "OTA updates", "Full Self-Driving"]  # Added feature not mentioned
            }
        }
    ]

def main():
    """
    Main function demonstrating extraction evaluation pipeline.
    
    This example shows:
    1. Sample extraction scenarios with various quality levels
    2. Evaluation prompt building
    3. Language model evaluation
    4. Results analysis and metrics
    """
    print("🔍 Extraction Evaluation Example")
    print("=" * 50)
    
    # Get sample evaluation scenarios
    scenarios = get_sample_evaluation_scenarios()
    print(f"📋 Evaluating {len(scenarios)} extraction scenarios...")
    
    # Build evaluation prompts
    prompt_builder = EvaluationPrompt()
    inputs = [prompt_builder.build(scenario) for scenario in scenarios]
    
    # Get language model for evaluation
    provider = "openai"
    model = "gpt-4o"  # Using more capable model for evaluation tasks
    
    print(f"🤖 Using {provider} language model: {model}")
    lm = get_language_model(provider=provider, model=model)
    
    # Setup metrics collection
    llm_collector = LLMMetricsCollector(
        name="extraction_evaluation",
        cost_per_input_token=2.5/1000000,   # GPT-4o input pricing
        cost_per_output_token=10/1000000    # GPT-4o output pricing
    )
    
    # Run evaluations
    print("⚡ Running evaluations...")
    results, effective_workers, evaluation_time = lm.generate(inputs)
    llm_collector.add_inference_time(evaluation_time)
    
    print(f"✅ Evaluation completed using {effective_workers} workers in {evaluation_time:.2f}s")
    
    # Display results
    print("\n📊 EVALUATION RESULTS")
    print("=" * 50)
    
    evaluations = []
    for i, (scenario, result) in enumerate(zip(scenarios, results)):
        print(f"\n🎯 Scenario {i+1}: {scenario['scenario_name']}")
        
        if result.get("error"):
            print(f"   ❌ Error: {result['error']}")
            continue
            
        evaluation = result.get("evaluation_result")
        if evaluation:
            evaluations.append(evaluation)
            print(f"   Correct: {'✅ Yes' if evaluation.get('correct') else '❌ No'}")
            print(f"   Formatted: {'✅ Yes' if evaluation.get('formatted') else '❌ No'}")
            if evaluation.get('confidence'):
                print(f"   Confidence: {evaluation.get('confidence'):.2f}")
            if evaluation.get('reasoning'):
                print(f"   Reasoning: {evaluation.get('reasoning')}")
    
    # Collect and display metrics
    llm_metrics = llm_collector.collect_metrics(results)
    
    print(f"\n📈 PERFORMANCE METRICS")
    print("=" * 50)
    print(llm_collector.format_summary(llm_metrics))
    
    # Schema-based analysis of evaluations
    if evaluations:
        schema_collector = SchemaMetricsCollector(BasicEvaluation, "evaluation_analysis")
        schema_metrics = schema_collector.collect_metrics(evaluations)

        print(f"\n🔍 EVALUATION QUALITY ANALYSIS")
        print("=" * 50)
        print(schema_collector.format_summary(schema_metrics))
    
    # Summary insights
    print(f"\n💡 SUMMARY")
    print("=" * 50)
    print(f"Scenarios evaluated: {len(scenarios)}")
    print(f"Successful evaluations: {len(evaluations)}")
    
    if evaluations:
        correct_count = sum(1 for e in evaluations if e.get('correct'))
        formatted_count = sum(1 for e in evaluations if e.get('formatted'))
        avg_confidence = sum(e.get('confidence', 0) for e in evaluations if e.get('confidence')) / len([e for e in evaluations if e.get('confidence')]) if any(e.get('confidence') for e in evaluations) else 0

        print(f"Correct extractions: {correct_count}/{len(evaluations)}")
        print(f"Properly formatted: {formatted_count}/{len(evaluations)}")
        if avg_confidence > 0:
            print(f"Average confidence: {avg_confidence:.2f}")
    
    print(f"Total evaluation cost: ${llm_metrics.get('total_cost', 0):.4f}")
    print(f"Average time per evaluation: {evaluation_time/len(scenarios):.2f}s")
    
    return {
        "evaluations": evaluations,
        "llm_metrics": llm_metrics,
        "schema_metrics": schema_metrics if evaluations else None,
        "scenarios": scenarios
    }

if __name__ == "__main__":
    main()
