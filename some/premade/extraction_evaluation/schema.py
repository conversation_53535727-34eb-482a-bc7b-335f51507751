"""
Extraction Evaluation Schema

Defines the structured output format for evaluating extraction results.
Uses a simple evaluation schema similar to the generic extraction example.
"""
from __future__ import annotations

from pydantic import BaseModel
from typing import Optional

class BasicEvaluation(BaseModel):
    """
    Simple evaluation of an extraction result.

    This schema provides a straightforward assessment of extraction quality
    focusing on correctness and formatting compliance.
    """
    correct: bool
    formatted: bool
    reasoning: Optional[str] = None
    confidence: Optional[float] = None
