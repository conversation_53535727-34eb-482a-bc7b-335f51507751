"""
Extraction Evaluation Prompt Builder

Defines the prompt template for evaluating extraction results against schemas.
Uses the BasePromptBuilder interface from the SOME library.
"""
from __future__ import annotations

from typing import Any, Dict
import json

from .schema import BasicEvaluation
from some.prompting import BasePromptBuilder

class EvaluationPrompt(BasePromptBuilder):
    """
    Prompt builder for evaluating extraction results.

    This prompt evaluates extractions on two key criteria:
    - Correct: Is the extracted information factually accurate?
    - Formatted: Does the output follow the expected schema properly?
    """
    
    def build(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build a prompt for evaluating an extraction result.
        
        Args:
            item: Dictionary containing:
                - 'original_text': The source text that was processed
                - 'extraction_prompt': The prompt used for extraction
                - 'expected_schema': The schema definition (as dict or string)
                - 'extraction_output': The actual extraction result
                - 'evaluation_context': Optional additional context
                
        Returns:
            Dictionary with prompt configuration for the language model
        """
        original_text = item["original_text"]
        extraction_prompt = item["extraction_prompt"]
        expected_schema = item["expected_schema"]
        extraction_output = item["extraction_output"]
        evaluation_context = item.get("evaluation_context", "")
        
        # Format schema for display
        if isinstance(expected_schema, dict):
            schema_display = json.dumps(expected_schema, indent=2)
        else:
            schema_display = str(expected_schema)
        
        # Format extraction output for display
        if isinstance(extraction_output, dict):
            output_display = json.dumps(extraction_output, indent=2)
        else:
            output_display = str(extraction_output)
        
        prompt_text = f"""Evaluate the extraction result on two key criteria:
1. Is the extracted information factually accurate based on the input text?
2. Does the output follow the expected format/schema properly?

Respond with your evaluation in the specified JSON format.

**ORIGINAL SOURCE TEXT:**
{original_text}

**EXTRACTION PROMPT USED:**
{extraction_prompt}

**EXPECTED SCHEMA:**
{schema_display}

**ACTUAL EXTRACTION OUTPUT:**
{output_display}

{f"**ADDITIONAL CONTEXT:**\n{evaluation_context}\n" if evaluation_context else ""}"""

        return {
            "prompt_text": prompt_text,
            "response_format": BasicEvaluation,
            "result_key": "evaluation_result",
        }
