"""
Extraction Evaluation - Pre-made Example

This package demonstrates how to evaluate extraction results given a schema,
prompt, and output using the SOME library's building blocks.

Key components:
- ExtractionEvaluation: Schema for evaluation results
- EvaluationPrompt: Prompt builder for evaluating extractions
- run_evaluation: Main execution script with sample evaluation scenarios

Example usage:
    from some.premade.extraction_evaluation import main
    results = main()

    # Or run directly
    python -m some.premade.extraction_evaluation.run_evaluation
"""

from .schema import BasicEvaluation
from .prompt import EvaluationPrompt
from .run_evaluation import main

__all__ = [
    "BasicEvaluation",
    "EvaluationPrompt",
    "main"
]
